Let Fn be the nth Fibonacci number defined by F0 = 0, F1 = 1, Fn = Fn−1 + Fn−2 for n ≥ 2.  
(a) Show that gcd(Fn+1, Fn) using <PERSON><PERSON><PERSON>’s algorithm takes n steps.  
(b) Does this show that the time complexity of <PERSON><PERSON><PERSON>’s algorithm is Ω(N) where N is the input size as defined in our lectures?  
(c) Why or why not?  
(d) Explain the asymptotic lower bound on the time complexity in terms of input size (as defined in the lecture) that you obtain from this example?