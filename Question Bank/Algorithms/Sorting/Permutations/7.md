A permutation x₁, …, xₙ of 1 to n is lexicographically smaller than a permutation y₁, …, yₙ if and only if there exists a 1 ≤ j ≤ n such that xᵢ = yᵢ for 1 ≤ i < j and xⱼ < yⱼ.  

(a) Show using the definition that 2, 1, 3, 4 is lexicographically smaller than 2, 1, 4, 3.  
(b) Prove that if a permutation π₁ is lexicographically smaller than a permutation π₂ and π₂ is smaller than another permutation π₃, then π₁ is smaller than π₃ using a direct proof.  
(c) What is the lexicographically next permutation (smallest permutation larger than) of 2, 1, 5, 6, 4, 3?  
(d) Describe the key ideas in a linear-time algorithm to compute the lexicographically next permutation. The input is an array A that contains the current permutation. Your algorithm has to modify the array A until it contains the next permutation. Do not write a program.  
(e) Describe how your algorithm works on the input 2, 1, 5, 6, 4, 3 in detail.