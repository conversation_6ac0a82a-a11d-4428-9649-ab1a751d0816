/* Dark Mode Question Bank Form with Cool Colors */

body {
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0d1117 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #e6edf3;
    min-height: 100vh;
}

.container-fluid {
    max-width: 1400px;
}

/* Card styling */
.card {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 0.75rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.card-header {
    background: linear-gradient(135deg, #21262d 0%, #30363d 100%);
    border-bottom: 1px solid #30363d;
    font-weight: 600;
    color: #58a6ff;
    border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Form styling */
.form-label {
    font-weight: 600;
    color: #f0f6fc;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.form-label i {
    margin-right: 0.5rem;
    color: #58a6ff;
}

.form-control, .form-select {
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 0.5rem;
    color: #e6edf3;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background-color: #161b22;
    border-color: #58a6ff;
    box-shadow: 0 0 0 0.25rem rgba(88, 166, 255, 0.25);
    color: #e6edf3;
}

.form-control::placeholder {
    color: #7d8590;
}

.form-select option {
    background-color: #161b22;
    color: #e6edf3;
}

/* Markdown editor styling */
#full_question_text {
    font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', Courier, monospace;
    font-size: 0.9rem;
    resize: none;
    background-color: #0d1117;
    border: 1px solid #30363d;
    color: #e6edf3;
}

#markdown-preview {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
    border: 1px solid #30363d;
    font-size: 0.9rem;
    line-height: 1.6;
    color: #e6edf3;
    border-radius: 0.5rem;
}

#markdown-preview h1, #markdown-preview h2, #markdown-preview h3 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: #58a6ff;
}

#markdown-preview h1 {
    font-size: 1.5rem;
    border-bottom: 2px solid #30363d;
    padding-bottom: 0.3rem;
    color: #79c0ff;
}

#markdown-preview h2 {
    font-size: 1.3rem;
    color: #58a6ff;
}

#markdown-preview h3 {
    font-size: 1.1rem;
    color: #7c3aed;
}

#markdown-preview code {
    background-color: #21262d;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
    color: #ff7b72;
    border: 1px solid #30363d;
}

#markdown-preview pre {
    background: linear-gradient(135deg, #0d1117 0%, #21262d 100%);
    border: 1px solid #30363d;
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

#markdown-preview pre code {
    background-color: transparent;
    padding: 0;
    color: #e6edf3;
    border: none;
}

#markdown-preview blockquote {
    border-left: 4px solid #58a6ff;
    padding-left: 1rem;
    margin-left: 0;
    color: #7d8590;
    background-color: rgba(88, 166, 255, 0.1);
    border-radius: 0 0.25rem 0.25rem 0;
}

#markdown-preview ul, #markdown-preview ol {
    padding-left: 2rem;
}

#markdown-preview li {
    margin-bottom: 0.25rem;
}

#markdown-preview table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    background-color: #161b22;
    border-radius: 0.5rem;
    overflow: hidden;
}

#markdown-preview th, #markdown-preview td {
    border: 1px solid #30363d;
    padding: 0.75rem;
    text-align: left;
}

#markdown-preview th {
    background: linear-gradient(135deg, #21262d 0%, #30363d 100%);
    font-weight: 600;
    color: #58a6ff;
}

/* Button styling */
.btn-primary {
    background: linear-gradient(135deg, #58a6ff 0%, #388bfd 100%);
    border: none;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(88, 166, 255, 0.3);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #388bfd 0%, #1f6feb 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6e7681 0%, #484f58 100%);
    border: none;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(110, 118, 129, 0.3);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #484f58 0%, #373e47 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(110, 118, 129, 0.4);
}

/* Validation styling */
.was-validated .form-control:valid,
.was-validated .form-select:valid {
    border-color: #3fb950;
    box-shadow: 0 0 0 0.25rem rgba(63, 185, 80, 0.25);
}

.was-validated .form-control:invalid,
.was-validated .form-select:invalid {
    border-color: #f85149;
    box-shadow: 0 0 0 0.25rem rgba(248, 81, 73, 0.25);
}

.invalid-feedback {
    color: #f85149;
}

.valid-feedback {
    color: #3fb950;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .col-md-6 {
        margin-bottom: 1rem;
    }
    
    #markdown-preview {
        min-height: 200px;
    }
}

/* Loading state */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Success/Error states */
.alert {
    border-radius: 0.5rem;
    border: 1px solid;
    backdrop-filter: blur(10px);
}

.alert-success {
    background: linear-gradient(135deg, rgba(63, 185, 80, 0.2) 0%, rgba(63, 185, 80, 0.1) 100%);
    border-color: #3fb950;
    color: #3fb950;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(248, 81, 73, 0.2) 0%, rgba(248, 81, 73, 0.1) 100%);
    border-color: #f85149;
    color: #f85149;
}

/* Modal styling */
.modal-content {
    background-color: #161b22;
    border-radius: 0.75rem;
    border: 1px solid #30363d;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    color: #e6edf3;
}

.modal-header {
    border-bottom: 1px solid #30363d;
    background: linear-gradient(135deg, #21262d 0%, #30363d 100%);
    border-radius: 0.75rem 0.75rem 0 0;
}

.modal-title {
    color: #58a6ff;
}

.btn-close {
    filter: invert(1);
}

/* Tooltip styling */
.tooltip {
    font-size: 0.875rem;
}

/* Custom scrollbar for preview */
#markdown-preview::-webkit-scrollbar {
    width: 8px;
}

#markdown-preview::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#markdown-preview::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#markdown-preview::-webkit-scrollbar-thumb:hover {
    background: #58a6ff;
}

/* Additional cool effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
}

/* Glowing effect for focused elements */
.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(88, 166, 255, 0.25),
                0 0 20px rgba(88, 166, 255, 0.1);
}

/* Cool gradient text for headings */
h1, h5 {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Animated background for body */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(88, 166, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Loading animation for buttons */
.btn:disabled {
    position: relative;
    overflow: hidden;
}

.btn:disabled::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Improved scrollbar for the entire page */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #0d1117;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #30363d 0%, #58a6ff 100%);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #58a6ff 0%, #79c0ff 100%);
}
