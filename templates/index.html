<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Bank Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4 fw-bold mb-3" style="background: linear-gradient(135deg, #58a6ff 0%, #79c0ff 50%, #a5a5f5 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <i class="bi bi-journal-text me-3"></i>
                        Question Bank Entry Form
                    </h1>
                    <p class="lead text-muted">Create and organize educational questions with markdown support</p>
                </div>
            </div>
        </div>

        <form id="questionForm" class="needs-validation" novalidate>
            <div class="row">
                <!-- Left Column - Form Fields -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Question Details</h5>
                        </div>
                        <div class="card-body">
                            <!-- Title -->
                            <div class="mb-3">
                                <label for="title" class="form-label">
                                    <i class="bi bi-tag"></i> Title / Short Label *
                                </label>
                                <input type="text" class="form-control" id="title" name="title" required
                                       placeholder="e.g., Binary Search Tree Insertion">
                                <div class="invalid-feedback">
                                    Please provide a title for the question.
                                </div>
                            </div>

                            <!-- Question Type -->
                            <div class="mb-3">
                                <label for="question_type" class="form-label">
                                    <i class="bi bi-list-check"></i> Question Type *
                                </label>
                                <select class="form-select" id="question_type" name="question_type" required>
                                    <option value="">Select question type...</option>
                                    {% for qtype in question_types %}
                                    <option value="{{ qtype }}">{{ qtype }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    Please select a question type.
                                </div>
                            </div>

                            <!-- Subject -->
                            <div class="mb-3">
                                <label for="subject" class="form-label">
                                    <i class="bi bi-book"></i> Subject *
                                </label>
                                <input type="text" class="form-control" id="subject" name="subject" required
                                       placeholder="e.g., Algorithms">
                                <div class="invalid-feedback">
                                    Please provide a subject.
                                </div>
                            </div>

                            <!-- Topic -->
                            <div class="mb-3">
                                <label for="topic" class="form-label">
                                    <i class="bi bi-bookmark"></i> Topic *
                                </label>
                                <input type="text" class="form-control" id="topic" name="topic" required
                                       placeholder="e.g., Graphs">
                                <div class="invalid-feedback">
                                    Please provide a topic.
                                </div>
                            </div>

                            <!-- Subtopic -->
                            <div class="mb-3">
                                <label for="subtopic" class="form-label">
                                    <i class="bi bi-bookmark-fill"></i> Subtopic
                                </label>
                                <input type="text" class="form-control" id="subtopic" name="subtopic"
                                       placeholder="e.g., Dijkstra's Algorithm">
                            </div>

                            <!-- Difficulty Level -->
                            <div class="mb-3">
                                <label for="difficulty_level" class="form-label">
                                    <i class="bi bi-speedometer2"></i> Difficulty Level *
                                </label>
                                <select class="form-select" id="difficulty_level" name="difficulty_level" required>
                                    <option value="">Select difficulty...</option>
                                    {% for level in difficulty_levels %}
                                    <option value="{{ level }}">{{ level }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    Please select a difficulty level.
                                </div>
                            </div>

                            <!-- Estimated Time -->
                            <div class="mb-3">
                                <label for="estimated_time" class="form-label">
                                    <i class="bi bi-clock"></i> Estimated Time to Solve (minutes) *
                                </label>
                                <input type="number" class="form-control" id="estimated_time" name="estimated_time" 
                                       min="1" max="300" required placeholder="e.g., 8">
                                <div class="invalid-feedback">
                                    Please provide an estimated time (1-300 minutes).
                                </div>
                            </div>

                            <!-- Bloom Level -->
                            <div class="mb-3">
                                <label for="bloom_level" class="form-label">
                                    <i class="bi bi-diagram-3"></i> Bloom's Taxonomy Level *
                                </label>
                                <select class="form-select" id="bloom_level" name="bloom_level" required>
                                    <option value="">Select Bloom's level...</option>
                                    {% for level in bloom_levels %}
                                    <option value="{{ level }}">{{ level }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    Please select a Bloom's taxonomy level.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Question Text with Markdown -->
                <div class="col-lg-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-pencil-square"></i> Full Question Text *
                            </h5>
                            <small class="text-muted">Supports Markdown formatting</small>
                        </div>
                        <div class="card-body">
                            <div class="row h-100">
                                <!-- Markdown Input -->
                                <div class="col-md-6 h-100">
                                    <label for="full_question_text" class="form-label">Markdown Input:</label>
                                    <textarea class="form-control h-75" id="full_question_text" name="full_question_text" 
                                              required placeholder="Enter your question here using Markdown...

Example:
# Question Title

Given a **Binary Search Tree**, insert the value `42` and show the resulting tree.

```python
def insert(root, value):
    # Your code here
    pass
```

- Option A: ...
- Option B: ..."></textarea>
                                    <div class="invalid-feedback">
                                        Please provide the full question text.
                                    </div>
                                </div>
                                
                                <!-- Markdown Preview -->
                                <div class="col-md-6 h-100">
                                    <label class="form-label">Live Preview:</label>
                                    <div id="markdown-preview" class="border rounded p-3 h-75 overflow-auto bg-light">
                                        <em class="text-muted">Preview will appear here as you type...</em>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="bi bi-check-circle"></i> Submit Question
                    </button>
                    <button type="reset" class="btn btn-secondary btn-lg ms-2">
                        <i class="bi bi-arrow-clockwise"></i> Reset Form
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Success/Error Modal -->
    <div class="modal fade" id="resultModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Result</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>
